import React from "react";
import { ToolDefinition, ToolGridProps } from "@/shared/types";
import { toolDefinitions } from "@/lib/fabric/tools";

const ToolGrid: React.FC<ToolGridProps> = ({
  activeMode,
  hasPerformedCrop,
  onToolSelect,
  onCrop,
  grayscale,
  invert,
  disableGrayscale,
  onRotate,
  onFlipHorizontal,
  onFlipVertical,
  onGrayscaleChange,
  onInvertChange,
}) => {
  const handleToolClick = async (tool: ToolDefinition) => {
    if (tool.type === "mode") {
      if (tool.mode === "crop" && hasPerformedCrop) {
        if (onCrop) {
          await onCrop();
          onToolSelect("crop");
        }
        return;
      }
      if (tool.mode) {
        onToolSelect(tool.mode);
      }
    } else if (tool.type === "action") {
      switch (tool.action) {
        case "rotate":
          onRotate?.();
          break;
        case "flipHorizontal":
          onFlipHorizontal?.();
          break;
        case "flipVertical":
          onFlipVertical?.();
          break;
      }
    } else if (tool.type === "toggle") {
      switch (tool.action) {
        case "grayscale":
          if (!disableGrayscale) {
            onGrayscaleChange?.(!grayscale);
          }
          break;
        case "invert":
          onInvertChange?.(!invert);
          break;
      }
    }
  };

  return (
    <div className="tool-grid">
      {toolDefinitions.map((tool, index) => {
        const IconComponent = tool.icon;
        const isCropInactive = tool.mode === "crop" && hasPerformedCrop;

        let isActive = false;
        let isDisabled = false;
        let buttonTitle = tool.title;

        if (tool.type === "mode") {
          isActive = activeMode === tool.mode;
          if (tool.mode === "crop" && hasPerformedCrop) {
            buttonTitle = "Restore original image";
          }
        } else if (tool.type === "toggle") {
          if (tool.action === "grayscale") {
            isActive = grayscale || false;
            isDisabled = disableGrayscale || false;
          } else if (tool.action === "invert") {
            isActive = invert || false;
          }
        }

        return (
          <button
            key={tool.mode || tool.action || index}
            className={`tool-btn ${isActive ? "active" : ""} ${isCropInactive ? "inactive" : ""} ${
              isDisabled ? "disabled" : ""
            }`}
            onClick={() => handleToolClick(tool)}
            disabled={isDisabled}
            title={buttonTitle}
          >
            <IconComponent />
          </button>
        );
      })}
    </div>
  );
};

export default ToolGrid;
