import { useCallback, useState, useMemo } from "react";
import { Canvas } from "fabric";
import { createMagnifier<PERSON><PERSON><PERSON> } from "@/lib/fabric/operations/magnifier";

export interface MagnifierManagementState {
  magnifier: boolean;
  handleMagnifierChange: (value: boolean) => void;
}

/*
Manages magnifier state as a simple UI toggle without backend dependency.
Uses local state to control magnifier activation and deactivation.
*/
export const useMagnifierManagement = (
  fabricCanvas: React.RefObject<Canvas | null>
): MagnifierManagementState => {
  const [magnifier, setMagnifier] = useState<boolean>(false);

  const handleMagnifierChange = useCallback(
    (value: boolean) => {
      const handler = createMagnifierHandler(fabricCanvas, setMagnifier);
      handler(value);
    },
    [fabricCanvas]
  );

  const magnifierManagement = useMemo(
    () => ({
      magnifier,
      handleMagnifierChange,
    }),
    [magnifier, handleMagnifierChange]
  );

  return magnifierManagement;
};
