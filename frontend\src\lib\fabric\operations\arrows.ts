import { Canvas, Group, Line, Triangle, util } from "fabric";
import { v4 as uuidv4 } from "uuid";

/*
Creates an arrow annotation as a Fabric Group composed of:
- Line (shaft) starting at [0,0] and extending toward drag direction
- Triangle (head) positioned/rotated based on drag angle
*/

type ArrowLine = Line & {
  id: string;
  name?: string;
  head?: Triangle;
  headSize?: number;
};

export const createArrow = (
  start: { x: number; y: number },
  config: any
): Group & { id: string } => {
  const line = new Line([start.x, start.y, start.x, start.y], {
    stroke: config.stroke,
    strokeWidth: config.strokeWidth,
    strokeUniform: true,
    selectable: true,
    evented: true,
    hasControls: false,
    perPixelTargetFind: true,
    hoverCursor: "move",
    name: "arrow",
    objectCaching: false,
  }) as ArrowLine;

  line.lockMovementX = false;
  line.lockMovementY = false;
  (line as any).skipTargetFind = false;

  line.id = uuidv4();
  line.headSize = Math.max(6, Number(config.arrowHeadSize ?? 14));

  return line as unknown as Group & { id: string };
};

const ensureHead = (line: ArrowLine): Triangle => {
  if (line.head && line.head.canvas) {
    (line.head as any).bringToFront?.();
    return line.head;
  }

  const size = Math.max(6, line.headSize ?? 14);
  const head = new Triangle({
    width: size,
    height: size,
    fill: (line.stroke as string) || "#ff0000",
    originX: "center",
    originY: "center",
    selectable: false,
    evented: false,
    name: "arrowHead",
    excludeFromExport: false,
  });
  (head as any).skipTargetFind = true;

  const c = line.canvas;
  if (c) {
    c.add(head);
    (head as any).bringToFront?.();
  }

  line.head = head;
  return head;
};

/*
Updates arrow geometry during drag:
- Computes angle from startPoint to currentPoint
- Repositions/rotates head; adjusts shaft endpoints to avoid head overlap
- Arrow head is always positioned at the currentPoint (end of drag)
*/
export const updateArrowSize = (
  group: Group,
  startPoint: { x: number; y: number },
  currentPoint: { x: number; y: number }
): void => {
  const line = group as unknown as ArrowLine;

  const x1 = startPoint.x;
  const y1 = startPoint.y;
  const x2 = currentPoint.x;
  const y2 = currentPoint.y;

  line.set({ x1, y1, x2, y2 });
  line.setCoords();

  const head = ensureHead(line);
  const size = Math.max(6, line.headSize ?? head.height ?? 14);

  // Arrow head is always at the current point (end of drag)
  const tipX = x2;
  const tipY = y2;
  const tailX = x1;
  const tailY = y1;

  const ang = Math.atan2(tipY - tailY, tipX - tailX) || 0;
  const angDeg = (ang * 180) / Math.PI;

  head.set({
    left: tipX + Math.cos(ang) * (size / 2),
    top: tipY + Math.sin(ang) * (size / 2),
    angle: angDeg - 90,
    fill: (line.stroke as string) || head.fill,
  });
  (head as any).bringToFront?.();
  head.setCoords();

  line.canvas?.requestRenderAll();
};

export const syncArrowFromSaved = (
  group: Group,
  item: any,
  width: number,
  height: number
): void => {
  const line = group as unknown as ArrowLine;

  const x1 = (item.x1Norm ?? 0) * width;
  const y1 = (item.y1Norm ?? 0) * height;
  const x2 = (item.x2Norm ?? 0) * width;
  const y2 = (item.y2Norm ?? 0) * height;

  line.set({ x1, y1, x2, y2 });
  if (item.id !== undefined) (line as any).id = item.id;
  if (item.arrowHeadSize) line.headSize = Math.max(6, Number(item.arrowHeadSize));
  line.setCoords();

  const head = ensureHead(line);
  const size = Math.max(6, line.headSize ?? 14);

  const tipX = x2;
  const tipY = y2;
  const tailX = x1;
  const tailY = y1;

  const ang = Math.atan2(tailY - tipY, tailX - tipX) || 0;
  const angDeg = (ang * 180) / Math.PI;

  head.set({
    left: tipX + Math.cos(ang) * (size / 2),
    top: tipY + Math.sin(ang) * (size / 2),
    angle: angDeg - 90,
    width: size,
    height: size,
    scaleX: 1,
    scaleY: 1,
    originX: "center",
    originY: "center",
    fill: (line.stroke as string) || head.fill,
  });

  head.setCoords();
  (head as any).bringToFront?.();

  line.canvas?.requestRenderAll();
};

//Reposition the arrowhead after the line has been modified
export const updateArrowOnModify = (canvas: Canvas, group: Group): void => {
  const line = group as unknown as ArrowLine;
  if (!line) return;

  const head = ensureHead(line);
  const size = Math.max(6, line.headSize ?? head.height ?? 14);

  const points = line.calcLinePoints();
  const matrix = line.calcTransformMatrix();
  const start = util.transformPoint({ x: points.x1, y: points.y1 }, matrix);
  const end = util.transformPoint({ x: points.x2, y: points.y2 }, matrix);

  const tipX = start.x;
  const tipY = start.y;
  const tailX = end.x;
  const tailY = end.y;

  const ang = Math.atan2(tailY - tipY, tailX - tipX);
  const angDeg = (ang * 180) / Math.PI;

  head.set({
    left: tipX + Math.cos(ang) * (size / 2),
    top: tipY + Math.sin(ang) * (size / 2),
    angle: angDeg - 90,
    fill: (line.stroke as string) || head.fill,
  });
  head.setCoords();
  (head as any).bringToFront?.();

  canvas.requestRenderAll();
};

export const isArrow = (obj: any): obj is Group => {
  return (obj as any)?.name === "arrow";
};
